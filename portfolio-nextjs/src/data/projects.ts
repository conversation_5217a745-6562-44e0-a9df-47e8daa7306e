import { Project } from '@/lib/types';

export const projects: Project[] = [
  {
    id: 'trade-book-ledge',
    title: 'Trade Book Ledge',
    description: 'A SaaS (Software as a Service) product for business management, targeting middlemen traders with subscription-based access model.',
    longDescription: 'A comprehensive SaaS ecosystem for business management targeting middlemen traders. Leveraged no-code platforms and AI tools (ChatGPT, Claude) to rapidly build a scalable cloud solution with web application, secure authentication, and data management.',
    technologies: ['React', 'TypeScript', 'Vite', 'Supabase'],
    features: [
      'Leveraged no-code platforms and AI tools (ChatGPT, Claude) to rapidly build a scalable cloud solution',
      'Created a complete SaaS ecosystem with web application (Vite, React, TypeScript)',
      'Integrated with Supabase for secure user authentication and data management',
      'Subscription-based access model for middlemen traders',
      'Real-time data synchronization',
      'Responsive design for all devices'
    ],
    githubUrl: 'https://github.com/nrenx/Trade-Book-Ledge',
    liveUrl: 'https://nrenx.github.io/Trade-Book-Ledge/',
    imageUrl: '/assets/images/projects/trade-book-ledge.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2023-08',
    endDate: '2023-11',
  },
  {
    id: 'nbkrist-portal',
    title: 'NBKRIST Student Portal',
    description: 'A student portal featuring academic information access, attendance tracking, and exam results.',
    longDescription: 'A comprehensive student portal for NBKRIST College featuring academic information access, attendance tracking, and exam results. Developed using AI tools and no-code approaches with modern technologies and automated deployment.',
    technologies: ['React', 'Tailwind CSS', 'Supabase', 'GitHub Pages'],
    features: [
      'Developed using AI tools and no-code approaches',
      'Integrated modern technologies (Vite, React, Tailwind CSS) and Supabase backend',
      'Created responsive design and automated deployment to GitHub Pages',
      'Academic information access system',
      'Attendance tracking functionality',
      'Exam results management',
      'Student dashboard with analytics'
    ],
    githubUrl: 'https://github.com/nrenx/nbkrist-student-portal.git',
    liveUrl: 'https://nbkrstudenthub.me/',
    imageUrl: '/assets/images/projects/nbkrist-portal.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2023-05',
    endDate: '2023-09',
  },
  {
    id: 'portfolio-website',
    title: 'Interactive Portfolio Website',
    description: 'Modern portfolio with macOS-style interface',
    longDescription: 'A modern, interactive portfolio website featuring a macOS-style interface, smooth animations, and responsive design. Built with React, Next.js, and Framer Motion with multi-language landing animation and dark/light theme switching.',
    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
    features: [
      'macOS-style interface simulation',
      'Multi-language landing animation',
      'Dark/light theme switching',
      'Smooth scroll animations',
      'Responsive design',
      'Contact form with validation',
      'Interactive project windows',
      'Draggable desktop folders'
    ],
    githubUrl: 'https://github.com/nrenx/portfolio',
    liveUrl: 'https://narendrachowdary.dev',
    imageUrl: '/assets/images/projects/portfolio.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2024-03',
    endDate: '2024-05',
  },
  {
    id: 'more-projects',
    title: 'More Projects',
    description: 'Check out my GitHub profile for more projects and code samples.',
    longDescription: 'Explore additional projects, code samples, and contributions on my GitHub profile. This includes various experiments, open-source contributions, and learning projects across different technologies and frameworks.',
    technologies: ['Various', 'Multiple Languages', 'Open Source'],
    features: [
      'Open source contributions',
      'Learning projects and experiments',
      'Code samples and snippets',
      'Collaborative projects',
      'Technology explorations',
      'Community contributions'
    ],
    githubUrl: 'https://github.com/nrenx',
    liveUrl: undefined,
    imageUrl: '/assets/images/projects/github-profile.jpg',
    category: 'other',
    status: 'in-progress',
    startDate: '2022-01',
    endDate: undefined,
  },
];

// Group projects by category
export const projectsByCategory = {
  web: projects.filter(p => p.category === 'web'),
  mobile: projects.filter(p => p.category === 'mobile'),
  automation: projects.filter(p => p.category === 'other'),
};

// Get featured projects
export const featuredProjects = projects.slice(0, 3);

// Get recent projects
export const recentProjects = projects
  .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
  .slice(0, 4);
